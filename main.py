import os
import numpy as np
import SimpleITK as sitk
import pyvista as pv
import imageio.v2 as iio

# ======================
# 读取 DICOM → LPS重排 + 等体素重采样 + 归一化
# ======================
def load_dicom_series(series_dir: str,
                      target_iso_mm: float | None = None,
                      clip_low_high=(2.0, 99.8),
                      gamma: float = 0.9):
    reader = sitk.ImageSeriesReader()
    series_ids = reader.GetGDCMSeriesIDs(series_dir)
    if not series_ids:
        raise RuntimeError(f"No DICOM series in: {series_dir}")

    file_names = reader.GetGDCMSeriesFileNames(series_dir, series_ids[0])
    reader.SetFileNames(file_names)
    img = reader.Execute()  # SimpleITK Image (with direction/origin/spacing)

    # 1) 统一到 LPS 方向（避免方向矩阵导致的朝向错位）
    img_lps = sitk.DICOMOrient(img, "LPS")

    # 2) 等体素重采样（TOF 常见各向异性，先变成等体素更接近机台MIP）
    spx = img_lps.GetSpacing()  # (sx, sy, sz) 物理单位 mm
    if target_iso_mm is None:
        target_iso_mm = min(spx)  # 取最小 spacing 作为等体素大小

    new_spacing = (target_iso_mm, target_iso_mm, target_iso_mm)
    size = img_lps.GetSize()  # (nx, ny, nz) in x,y,z
    new_size = [
        int(round(size[i] * (spx[i] / new_spacing[i])))
        for i in range(3)
    ]

    resampler = sitk.ResampleImageFilter()
    resampler.SetReferenceImage(img_lps)
    resampler.SetOutputSpacing(new_spacing)
    resampler.SetSize(new_size)
    resampler.SetOutputDirection(img_lps.GetDirection())
    resampler.SetOutputOrigin(img_lps.GetOrigin())
    # 连续型强度，重采样用BSpline，边界以常数0填充
    resampler.SetInterpolator(sitk.sitkBSpline)
    resampler.SetDefaultPixelValue(0.0)
    img_iso = resampler.Execute(img_lps)

    # 3) 转 numpy (z,y,x)
    vol_zyx = sitk.GetArrayFromImage(img_iso).astype(np.float32)

    # 4) 强度归一化（鲁棒百分位 + gamma）
    p_low, p_high = np.percentile(vol_zyx, clip_low_high)
    vol_zyx = np.clip(vol_zyx, p_low, p_high)
    vol_zyx = (vol_zyx - p_low) / (p_high - p_low + 1e-6)
    if gamma is not None and gamma > 0:
        vol_zyx = np.power(vol_zyx, gamma)

    # 5) 提供给 VTK 的网格（方向已对齐到LPS且等体素 → 方向矩阵可近似视作单位）
    vol_xyz = np.ascontiguousarray(np.transpose(vol_zyx, (2, 1, 0)))  # (x,y,z)
    nx, ny, nz = vol_xyz.shape

    grid = pv.ImageData()
    grid.dimensions = (nx, ny, nz)
    grid.spacing = new_spacing  # 等体素
    grid.origin = img_iso.GetOrigin()
    grid.point_data["scalars"] = vol_xyz.ravel(order="F")

    return grid, vol_zyx, new_spacing

# ======================
# 依据患者轴向生成 MIP（符合机台视图）
# LPS坐标：+X=Left，+Y=Posterior，+Z=Superior
# - AP视图：沿 +Y 投影，显示(X,Z)，左在图像右侧，上为Superior
# - LAT视图：沿 +X 投影，显示(Y,Z)，左图=Anterior，上为Superior（左侧位）
# ======================
def _to_uint(img, bit_depth=8):
    img = np.clip(img, 0, 1)
    if bit_depth == 16:
        return (img * 65535).astype(np.uint16)
    return (img * 255).astype(np.uint8)

def save_mip_pngs(vol_zyx: np.ndarray,
                  out_prefix="tof_mip",
                  bit_depth=8):
    # AP（沿 +Y 投影）：axis=1
    ap = vol_zyx.max(axis=1)         # 输出 shape: (z, x)
    # 放射学显示：Superior 在上 → 翻转垂直轴；患者左在右 → 翻转水平轴(x)
    ap = np.flipud(ap)               # z: Superior→顶
    ap = np.fliplr(ap)               # x: Left→在右
    iio.imwrite(f"{out_prefix}_AP.png", _to_uint(ap, bit_depth))

    # LAT（左侧位，沿 +X 投影）：axis=2
    lat = vol_zyx.max(axis=2)        # 输出 shape: (z, y)
    # 约定：Superior 在上（flipud）；Anterior 在左（y 轴正向为 Posterior，所以无需左右翻转）
    lat = np.flipud(lat)
    iio.imwrite(f"{out_prefix}_LAT.png", _to_uint(lat, bit_depth))

    # 可选：SI（沿 +Z 投影），便于核对
    si = vol_zyx.max(axis=0)         # 输出 shape: (y, x)
    # 约定：显示为 A(上)–P(下)，R(左)–L(右) 的放射学图（把Left放到右侧）
    si = np.flipud(si)               # y: Anterior→顶
    si = np.fliplr(si)               # x: Left→右
    iio.imwrite(f"{out_prefix}_SI.png", _to_uint(si, bit_depth))

    print(f"Saved: {out_prefix}_AP.png, {out_prefix}_LAT.png, {out_prefix}_SI.png")

# ======================
# GPU MIP 旋转视频（PyVista/VTK）
# ======================
def make_mip_spin_video(
    grid: pv.ImageData,
    out_path: str = "tof3d_mip_spin.mp4",
    seconds: int = 10,
    fps: int = 30,
    window_size=(1280, 960),
    codec_priority=("h264_videotoolbox", "libx264", "mpeg4"),
    quality: int = 7
):
    n_frames = seconds * fps
    az_step = 360.0 / n_frames

    pv.global_theme.smooth_shading = False
    pl = pv.Plotter(off_screen=True, window_size=window_size)
    pl.set_background("black")

    # 体渲染启用最大强度投影
    vol = pl.add_volume(
        grid,
        cmap="gray",
        shade=False,
        opacity="linear",
        sampling_distance=0.5,  # 采样稍密一点，减少走样
    )
    vol.mapper.SetBlendModeToMaximumIntensity()

    # 初始设为类 AP 视角，再开始旋转
    pl.view_yx()  # 摄像机朝向近似 AP（也可用 pl.view_isometric()）
    pl.reset_camera()

    # 尝试不同编码器，避免平台不支持时报错
    last_err = None
    for codec in codec_priority:
        try:
            pl.open_movie(out_path, framerate=fps, quality=quality, codec=codec)
            break
        except Exception as e:
            last_err = e
            continue
    if pl._movie_writer is None:
        raise RuntimeError(f"Failed to open movie with codecs {codec_priority}: {last_err}")

    pl.show(auto_close=False)
    pl.render()
    pl.write_frame()

    for _ in range(n_frames - 1):
        pl.camera.azimuth += az_step
        pl.render()
        pl.write_frame()

    pl.close()
    print(f"Saved: {os.path.abspath(out_path)}")

# ======================
# 主函数
# ======================
if __name__ == "__main__":
    DICOM_DIR = "./tof3d_tra_uCS_701"

    grid, vol_zyx, _ = load_dicom_series(
        DICOM_DIR,
        target_iso_mm=None,        # 默认取 min spacing
        clip_low_high=(2.0, 99.8), # 可微调为 (1.0, 99.9)
        gamma=0.9                  # <1 提升小血管；=1 关闭gamma
    )

    # 静态 MIP（AP/LAT/SI 三张，符合机台视图）
    save_mip_pngs(vol_zyx, out_prefix="tof_mip", bit_depth=8)

    # 旋转视频（带编码器回退）
    make_mip_spin_video(
        grid,
        out_path="tof3d_mip_spin.mp4",
        seconds=12,
        fps=30,
        codec_priority=("h264_videotoolbox", "libx264", "mpeg4"),
        window_size=(1280, 960),
        quality=7
    )
